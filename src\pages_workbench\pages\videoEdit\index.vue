<template>
  <view class="page">
    <up-navbar
      @leftClick="goBack"
      :title="pageTitle"
      :safeAreaInsetTop="true"
      leftIcon="arrow-left"
      leftText="返回"
      leftIconColor="#1a1a1a"
      :border="false"
      :placeholder="true"
      titleStyle="color: #1a1a1a; font-weight: 600; font-size: 18px;"
    />
    <!-- 主要内容区域 -->
    <scroll-view class="container" scroll-y="true">
      <!-- 任务信息卡片 -->
      <view class="card">
        <view class="card-content">
          <view class="form-item">
            <view class="form-label">
              <text class="label-text">任务名称</text>
              <text class="label-required">*</text>
            </view>
            <up-input
              v-model="taskName"
              placeholder="请输入任务名称"
              :border="false"
              :customStyle="{
                backgroundColor: '#f8fafc',
                borderRadius: '16rpx',
                padding: '24rpx 20rpx',
                fontSize: '28rpx',
              }"
              maxlength="50"
              :showWordLimit="true"
            />
          </view>

          <view class="form-item">
            <view class="form-label">
              <text class="label-text">任务描述</text>
            </view>
            <up-textarea
              v-model="taskDescription"
              placeholder="请输入任务描述（可选）"
              :border="false"
              :customStyle="{
                backgroundColor: '#f8fafc',
                borderRadius: '16rpx',
                padding: '24rpx 20rpx',
                fontSize: '28rpx',
              }"
              maxlength="200"
              :showWordLimit="true"
              height="120rpx"
              autoHeight
            />
          </view>
        </view>
      </view>

      <view class="card-content">
        <TemplateSelector v-model="template" placeholder="点击选择模版" />
      </view>

      <!-- 视频上传卡片 -->
      <VideoUploadCard
        v-model="videoList"
        :max-count="getMaxMediaCount()"
        :has-template="!!template"
        :template-name="template?.Name || ''"
        :disabled="isUploadDisabled"
        :is-uploading="isUploading"
        :upload-progress="uploadProgress"
        @after-read="afterRead"
        @delete="deletePic"
        @upload="handleUploadTrigger"
        @preview="handleVideoPreview"
      />

      <!-- 操作按钮 -->
      <view class="action-section">
        <up-button
          type="primary"
          size="large"
          @click="startSynthesis"
          :disabled="!canStartSynthesis"
          class="synthesis-btn"
        >
          <text class="btn-icon">✨</text>
          <text class="btn-text">开始合成</text>
        </up-button>
      </view>
    </scroll-view>

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model:visible="showDeleteDialog"
      :showIcon="true"
      iconColor="#ff4757"
      :showFileInfo="true"
      :fullFileName="deleteFileFullName"
      :displayFileName="deleteFileDisplayName"
      color="#ff4757"
      @confirm="handleConfirmDelete"
      @cancel="handleCancelDelete"
    />

    <!-- 视频操作选择器 -->
    <up-action-sheet
      v-model:show="showVideoActionSheet"
      :actions="videoActionList"
      cancelText="取消"
      @select="handleVideoActionSelect"
      :round="10"
    />
  </view>
</template>

<script setup>
import { ref, computed } from "vue";

import modal from "@/plugins/modal";
import TemplateSelector from "@/components/platform/template-selector.vue";
import VideoUploadCard from "@/components/platform/video-upload-card.vue";
import ConfirmDialog from "@/components/platform/confirm-dialog.vue";

import { synthesizeVideo } from "@/api/platform/videoEdit";
import { onLoad, onShow } from "@dcloudio/uni-app";

const template = ref(null);
const videoList = ref([]);

// 任务信息
const taskName = ref("");
const taskDescription = ref("");

// 上传相关状态
const isUploading = ref(false);
const uploadProgress = ref(0);

// 删除确认对话框相关状态
const showDeleteDialog = ref(false);
const deleteFileFullName = ref("");
const deleteFileDisplayName = ref("");
const pendingDeleteFile = ref(null);
const pendingDeleteIndex = ref(-1);

// 视频操作弹窗相关状态
const showVideoActionSheet = ref(false);
const currentVideoFile = ref(null);
const videoActionList = ref([{ name: "播放视频" }]);

// 页面参数
const pageParams = ref({
  from: "",
  projectId: "",
  title: "",
});

// 是否需要重置数据的标志
const needsReset = ref(true);

onLoad((option) => {
  pageParams.value.from = option.from || "";
  pageParams.value.projectId = option.projectId || "";
  pageParams.value.title = option.title || "";

  // 首次加载时刷新数据
  refreshPageData();
  // 标记已经初始化，后续不需要重置
  needsReset.value = false;
});

// 刷新页面数据的函数
const refreshPageData = () => {
  // 重置模板选择
  template.value = null;
  // 清空视频列表
  videoList.value = [];
  // 重置任务信息
  taskName.value = "";
  taskDescription.value = "";
  // 重置上传状态
  isUploading.value = false;
  uploadProgress.value = 0;
  // 清理删除相关状态
  showDeleteDialog.value = false;
  deleteFileFullName.value = "";
  deleteFileDisplayName.value = "";
  pendingDeleteFile.value = null;
  pendingDeleteIndex.value = -1;
};

// 手动重置数据的方法（供外部调用）
const resetData = () => {
  refreshPageData();
  console.log("手动重置页面数据");
};

// 页面显示时的处理
onShow(() => {
  console.log("页面显示，needsReset:", needsReset.value);

  // 只有在需要重置时才重置数据
  if (needsReset.value) {
    console.log("重置页面数据");
    refreshPageData();
    needsReset.value = false;
  }
  console.log("页面显示，当前模板:", template.value?.Name || "未选择");
});

// 页面标题
const pageTitle = computed(() => {
  if (pageParams.value.projectId && pageParams.value.title) {
    return `编辑工程 - ${decodeURIComponent(pageParams.value.title)}`;
  }
  return "创建新工程";
});

// 是否可以开始合成
const canStartSynthesis = computed(() => {
  if (!template.value) {
    return false;
  }

  const requiredVideoCount = getMaxMediaCount();
  const currentVideoCount = videoList.value.length;

  // 如果模板不需要视频，可以直接合成
  if (requiredVideoCount === 0) {
    return true;
  }

  // 必须上传完全匹配数量的视频才能合成
  return currentVideoCount === requiredVideoCount;
});

// 计算是否禁用上传组件（只在其他情况下禁用，不因为没有模板而禁用）
const isUploadDisabled = computed(() => {
  // 这里可以添加其他禁用条件，比如正在上传时
  return isUploading.value;
});

/**处理文件上传后的回调 */
const afterRead = (event) => {
  const { file } = event;
  const fileList = Array.isArray(file) ? file : [file];

  fileList.forEach((item) => {
    // 确保文件对象有必要的属性
    const videoFile = {
      ...item,
      // 确保有文件名
      name: item.name || `视频_${Date.now()}`,
      // 确保有文件大小
      size: item.size || 0,
      // 确保有文件类型
      type: item.type || "video/mp4",
      // 如果没有url但有path，可以使用path作为url
      url: item.url || item.path,
      // 保留原始路径
      path: item.path,
    };

    videoList.value.push(videoFile);
  });
};

// 格式化文件名，处理过长的文件名
const formatFileName = (fileName, maxLength = 30) => {
  if (!fileName) return "未命名视频";

  if (fileName.length <= maxLength) {
    return fileName;
  }

  // 如果文件名过长，显示前面部分...后面部分
  const start = fileName.substring(0, Math.floor(maxLength * 0.6));
  const end = fileName.substring(fileName.length - Math.floor(maxLength * 0.3));
  return `${start}...${end}`;
};

/**删除已上传的视频 */
const deletePic = (file, index) => {
  // 保存待删除的文件信息
  pendingDeleteFile.value = file;
  pendingDeleteIndex.value = index;

  // 设置文件名信息
  const fileName = file.name || "未命名视频";
  deleteFileFullName.value = fileName;
  deleteFileDisplayName.value = formatFileName(fileName);

  // 显示确认对话框
  showDeleteDialog.value = true;
};

// 确认删除
const handleConfirmDelete = () => {
  const file = pendingDeleteFile.value;
  const index = pendingDeleteIndex.value;

  if (!file) {
    return;
  }

  try {
    // 执行删除操作
    if (
      typeof index === "number" &&
      index >= 0 &&
      index < videoList.value.length
    ) {
      // 使用索引删除
      videoList.value.splice(index, 1);
      modal.msg("删除成功");
    } else {
      // 使用过滤删除（备用方案）
      const originalLength = videoList.value.length;
      videoList.value = videoList.value.filter((item) => {
        return !(
          (item.url && file.url && item.url === file.url) ||
          (item.path && file.path && item.path === file.path) ||
          (item.name &&
            file.name &&
            item.name === file.name &&
            item.size === file.size)
        );
      });

      if (videoList.value.length < originalLength) {
        modal.msg("删除成功");
      } else {
        modal.msg("删除失败：未找到匹配的文件");
      }
    }

    // 这里可以添加删除服务器文件的逻辑
    // if (file.url) {
    //   await deleteVideoFromServer(file.url);
    // }
  } catch (e) {
    modal.msg("删除失败");
  } finally {
    // 清理状态
    pendingDeleteFile.value = null;
    pendingDeleteIndex.value = -1;
    deleteFileFullName.value = "";
    deleteFileDisplayName.value = "";
  }
};

// 取消删除
const handleCancelDelete = () => {
  // 清理状态
  pendingDeleteFile.value = null;
  pendingDeleteIndex.value = -1;
  deleteFileFullName.value = "";
  deleteFileDisplayName.value = "";
};

/**返回工作台 */
const navigateToWorkbench = () => {
  try {
    // 标记需要重置数据，这样下次进入页面时会刷新
    needsReset.value = true;

    // 如果是从工作台跳转过来的，直接返回；否则跳转到工作台
    if (pageParams.value.from == "work") {
      console.log("发送刷新工作台列表事件");
      uni.$emit("refreshWorkList");
      uni.navigateBack({ delta: 1 });
    } else {
      uni.switchTab({
        url: "/pages/work",
      });
    }
  } catch (error) {
    console.error("返回工作台失败:", error);
    uni.switchTab({
      url: "/pages/work",
    });
  }
};

/**页面返回按钮 */
const goBack = () => {
  navigateToWorkbench();
};

// 处理上传触发事件
const handleUploadTrigger = () => {};

// 处理视频预览
const handleVideoPreview = (file, index) => {
  if (!file.url) return;

  // 保存当前操作的视频文件
  currentVideoFile.value = file;
  // 显示操作选择器
  showVideoActionSheet.value = true;
};

// 处理视频操作选择
const handleVideoActionSelect = (item) => {
  const file = currentVideoFile.value;
  if (!file || !file.url) return;

  // 播放视频
  playVideo(file.url, file);
};

// 播放视频函数
const playVideo = (videoUrl, file) => {
  // #ifdef APP-PLUS
  // App环境：使用系统默认播放器
  plus.runtime.openFile(videoUrl, {}, (error) => {
    uni.showModal({
      title: "播放失败",
      content: "无法播放此视频文件，可能是格式不支持或文件损坏",
      showCancel: false,
    });
  });
  // #endif
};

/**校验表单 */
const validateForm = () => {
  // 验证任务名称
  if (!taskName.value || taskName.value.trim() === "") {
    modal.msg("请输入任务名称");
    return false;
  }

  if (!template.value) {
    modal.msg("请选择模板");
    return false;
  }

  const requiredVideoCount = getMaxMediaCount();
  const currentVideoCount = videoList.value.length;

  // 如果模板不需要视频，直接通过验证
  if (requiredVideoCount === 0) {
    return true;
  }

  // 检查是否有上传视频
  if (currentVideoCount === 0) {
    modal.msg(`请上传视频，当前模板需要 ${requiredVideoCount} 个视频`);
    return false;
  }

  // 检查视频数量是否不足
  if (currentVideoCount < requiredVideoCount) {
    modal.msg(
      `视频数量不足，还需要上传 ${
        requiredVideoCount - currentVideoCount
      } 个视频 (当前 ${currentVideoCount}/${requiredVideoCount})`
    );
    return false;
  }

  // 检查视频数量是否过多
  if (currentVideoCount > requiredVideoCount) {
    modal.msg(
      `视频数量过多，请删除 ${
        currentVideoCount - requiredVideoCount
      } 个多余的视频 (当前 ${currentVideoCount}/${requiredVideoCount})`
    );
    return false;
  }

  return true;
};

/**开始合成视频 */
const startSynthesis = async () => {
  if (!validateForm()) return;
  modal.loading("提交中...");
  try {
    const files = [];

    videoList.value.forEach((item) => {
      files.push({
        name: item.name,
        uri: item.url,
      });
    });

    //  拼装的数据是什么
    const projectMetadata = {
      Title: taskName.value?.trim() || "",
      Description: taskDescription.value?.trim() || "",
    };
    const params = {
      templateId: template.value.TemplateId,
      clipsParam: template.value.ClipsParam,
      files: files,
      projectMetadata: JSON.stringify(projectMetadata)
    };

    const res = await synthesizeVideo(params);

    //拼接参数

    modal.closeLoading();

    // 合成成功后，标记需要重置数据
    needsReset.value = true;

    // 显示成功提示并自动返回工作台
    uni.showToast({
      title: "提交成功",
      icon: "success",
      duration: 2000,
      success: () => {
        // 成功提示显示后自动返回工作台
        setTimeout(() => {
          navigateToWorkbench();
        }, 1500);
      },
    });
  } catch (e) {
    modal.closeLoading();

    // 显示更友好的错误信息
    const errorMessage =
      e?.message || e?.data?.message || "合成失败，请稍后重试";
    modal.msg(errorMessage);

    // 可以选择是否在失败时也提供返回选项
    setTimeout(() => {
      uni.showModal({
        title: "合成失败",
        content: "是否返回工作台？",
        confirmText: "返回",
        cancelText: "留在此页",
        success: (res) => {
          if (res.confirm) {
            navigateToWorkbench();
          }
        },
      });
    }, 2000);
  }
};
// 计算最大允许上传的媒体文件数量
const getMaxMediaCount = () => {
  if (!template.value || !template.value.ClipsParam) {
    return 0;
  }

  try {
    const clipsParam = JSON.parse(template.value.ClipsParam);
    return Object.values(clipsParam).filter((value) => value === "mediaId")
      .length;
  } catch (e) {
    return 0;
  }
};
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  flex-direction: column;

  .container {
    flex: 1;
    padding: 24rpx;

    // 卡片通用样式
    .card {
      background: #ffffff;
      border-radius: 24rpx;
      margin-bottom: 32rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
      border: 1rpx solid rgba(0, 0, 0, 0.04);
      overflow: hidden;
      transition: all 0.3s ease;
      margin: 0 20rpx 32rpx;
      .card-header {
        padding: 32rpx 32rpx 24rpx;
        border-bottom: 1rpx solid #f1f5f9;

        .card-title {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;

          .title-icon {
            font-size: 32rpx;
            margin-right: 16rpx;
          }

          .title-text {
            font-size: 32rpx;
            font-weight: 600;
            color: #1e293b;
            line-height: 1.2;
          }
        }

        .card-subtitle {
          font-size: 26rpx;
          color: #64748b;
          line-height: 1.4;
        }
      }

      .card-content {
        padding: 32rpx;
      }
    }

    // 表单样式
    .form-item {
      margin-bottom: 32rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .form-label {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .label-text {
          font-size: 28rpx;
          font-weight: 500;
          color: #374151;
          line-height: 1.4;
        }

        .label-required {
          font-size: 28rpx;
          color: #ef4444;
          margin-left: 4rpx;
        }
      }
    }

    // 上传组件样式已移至 video-upload-card.vue 组件中

    // 操作按钮区域
    .action-section {
      padding: 40rpx 0;

      .synthesis-btn {
        width: 100%;
        height: 96rpx;
        border-radius: 20rpx;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12rpx;
        transition: all 0.3s ease;

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.3);
        }

        &:disabled {
          background: #e2e8f0;
          box-shadow: none;

          .btn-text,
          .btn-icon {
            color: #94a3b8 !important;
          }
        }

        .btn-icon {
          font-size: 32rpx;
          color: #ffffff;
        }

        .btn-text {
          font-size: 32rpx;
          font-weight: 600;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
