<template>
  <view class="scenic-detail">
    <!-- 顶部图片轮播 -->
    <view class="hero-section">
      <swiper class="hero-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
        <swiper-item v-for="(image, index) in scenicInfo.images" :key="index">
          <image class="hero-image" :src="image" mode="aspectFill"></image>
        </swiper-item>
      </swiper>
      
      <!-- 返回按钮 -->
      <view class="back-btn" @click="goBack">
        <view class="back-arrow"></view>
      </view>
    </view>

    <!-- 景区基本信息 -->
    <view class="info-section">
      <view class="basic-info">
        <text class="scenic-name">{{ scenicInfo.name }}</text>
        <view class="rating-distance">
          <view class="distance">
            <text class="distance-text">距您约{{ scenicInfo.distance }}</text>
          </view>
        </view>
        <text class="scenic-desc">{{ scenicInfo.description }}</text>
      </view>
      
      <!-- 快捷操作按钮 -->
      <view class="action-buttons">
        <view class="action-btn primary" @click="handleCheckin">
          <view class="video-icon"></view>
          <text class="btn-text">获取视频</text>
        </view>
        <view class="action-btn secondary" @click="handleNavigation">
          <view class="nav-icon"></view>
          <text class="btn-text">导航</text>
        </view>
      </view>
    </view>

    <!-- 景区详细信息 -->
    <view class="detail-sections">

      <!-- 地址信息 -->
      <view class="detail-card">
        <view class="card-header">
          <text class="card-title">详细地址</text>
        </view>
        <text class="card-content">{{ scenicInfo.address }}</text>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area"></view>
  </view>
</template>

<script setup>
import { reactive, onMounted } from 'vue'

// 景区信息
const scenicInfo = reactive({
  id: 1,
  name: '中国孙子文化园',
  rating: 4.8,
  reviewCount: 1256,
  distance: '126.7km',
  description: '中国孙子文化园是以春秋时期伟大军事家孙武及其《孙子兵法》为主题的文化园区，集文化展示、休闲娱乐、教育体验于一体。园区内设有孙子文化展览馆、兵法体验区、古战场复原等多个主题区域。',
  images: [
    'https://psyangji.com/wp-content/uploads/2019/12/141e67e02cc755.jpg',
    'https://js.design/special/img/appletpage-design/1665716999860.png',
    'https://js.design/special/img/appletpage-design/1665716999911.png'
  ],
  openTime: '08:00-18:00（全年开放）',
  tickets: [
    { type: '成人票', price: 80 },
    { type: '学生票', price: 40 },
    { type: '儿童票', price: 20 }
  ],
  phone: '0531-12345678',
  address: '山东省济南市历城区孙子文化园路1号'
})

// 获取页面参数
const getPageParams = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.id) {
    scenicInfo.id = parseInt(options.id)
  }
  if (options.name) {
    scenicInfo.name = decodeURIComponent(options.name)
  }

  // 根据ID加载不同的景区数据
  loadScenicData(scenicInfo.id)
}

// 根据ID加载景区数据
const loadScenicData = (id) => {
  const scenicData = {
    1: {
      name: '中国孙子文化园',
      rating: 4.8,
      reviewCount: 1256,
      distance: '126.7km',
      description: '中国孙子文化园是以春秋时期伟大军事家孙武及其《孙子兵法》为主题的文化园区，集文化展示、休闲娱乐、教育体验于一体。',
      openTime: '08:00-18:00（全年开放）',
      tickets: [
        { type: '成人票', price: 80 },
        { type: '学生票', price: 40 },
        { type: '儿童票', price: 20 }
      ],
      phone: '0531-12345678',
      address: '山东省济南市历城区孙子文化园路1号'
    },
    2: {
      name: '夫子山风景区',
      rating: 4.6,
      reviewCount: 892,
      distance: '282.1km',
      description: '夫子山风景区以其秀美的自然风光和深厚的文化底蕴而闻名，是集山水观光、文化体验、休闲度假于一体的综合性景区。',
      openTime: '07:30-17:30（全年开放）',
      tickets: [
        { type: '成人票', price: 60 },
        { type: '学生票', price: 30 },
        { type: '儿童票', price: 15 }
      ],
      phone: '0531-87654321',
      address: '山东省济南市长清区夫子山路88号'
    }
    // 可以继续添加更多景区数据
  }

  const data = scenicData[id] || scenicData[1]
  Object.assign(scenicInfo, { id, ...data })
}

// 页面方法
const goBack = () => {
  uni.navigateBack()
}



const handleCheckin = () => {
  console.log('获取视频，跳转到模板合成界面')
  // 跳转到模板合成界面，传递景区信息
  uni.navigateTo({
    url: `/pages_workbench/pages/videoEdit/index?scenicId=${scenicInfo.id}&scenicName=${encodeURIComponent(scenicInfo.name)}&from=scenic`
  })
}

const handleNavigation = () => {
  console.log('导航到景区')
  // 这里可以添加导航功能
}



// 页面加载时获取景区详情
onMounted(() => {
  getPageParams()
  console.log('当前景区信息:', scenicInfo)
})
</script>

<style lang="scss" scoped>
.scenic-detail {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 顶部图片轮播 */
.hero-section {
  position: relative;
  height: 300px;

  .hero-swiper {
    width: 100%;
    height: 100%;

    .hero-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .back-btn {
    position: absolute;
    top: 44px;
    left: 16px;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    z-index: 10;

    .back-arrow {
      width: 12px;
      height: 12px;
      border-left: 2px solid #fff;
      border-bottom: 2px solid #fff;
      transform: rotate(45deg);
      margin-left: 2px;
    }
  }


}

/* 基本信息区域 */
.info-section {
  background: #fff;
  margin-top: -20px;
  border-radius: 20px 20px 0 0;
  padding: 24px 20px;
  position: relative;
  z-index: 5;

  .basic-info {
    margin-bottom: 24px;

    .scenic-name {
      font-size: 24px;
      font-weight: 700;
      color: #333;
      margin-bottom: 12px;
      display: block;
    }

    .rating-distance {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .rating {
        display: flex;
        align-items: center;
        gap: 4px;

        .rating-text {
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        .review-count {
          font-size: 12px;
          color: #999;
        }
      }

      .distance {
        display: flex;
        align-items: center;
        gap: 4px;

        .distance-text {
          font-size: 12px;
          color: #999;
        }
      }
    }

    .scenic-desc {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
      display: block;
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;

    .action-btn {
      flex: 1;
      height: 48px;
      border-radius: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      transition: all 0.3s ease;

      &.primary {
        background: linear-gradient(135deg, #9AFF02 0%, #C8FF02 100%);
        
        .btn-text {
          color: #333;
          font-weight: 600;
        }

        &:active {
          transform: scale(0.98);
        }
      }

      &.secondary {
        background: #f8f9fa;
        border: 1px solid #e9ecef;

        .btn-text {
          color: #666;
          font-weight: 500;
        }

        &:active {
          background: #e9ecef;
        }
      }

      .btn-text {
        font-size: 14px;
      }

      /* CSS导航图标 */
      .nav-icon {
        width: 18px;
        height: 18px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 2px;
          left: 50%;
          transform: translateX(-50%);
          width: 8px;
          height: 8px;
          border: 2px solid #9AFF02;
          border-radius: 50%;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 4px solid transparent;
          border-right: 4px solid transparent;
          border-top: 6px solid #9AFF02;
        }
      }

      /* CSS视频图标 - 现代播放按钮 */
      .video-icon {
        width: 18px;
        height: 18px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        /* 外圆环 */
        &::before {
          content: '';
          position: absolute;
          width: 16px;
          height: 16px;
          background: transparent;
          border-radius: 50%;
          border: 2px solid #fff;
          box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3);
        }

        /* 播放三角形 */
        &::after {
          content: '';
          position: absolute;
          width: 0;
          height: 0;
          border-left: 6px solid #fff;
          border-top: 4px solid transparent;
          border-bottom: 4px solid transparent;
          margin-left: 2px;
          filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }
      }

    }
  }
}

/* 详细信息区域 */
.detail-sections {
  padding: 0 20px 20px 20px;

  .detail-card {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    margin-top: 30rpx;

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .card-content {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
      display: block;
    }

    .ticket-info {
      .ticket-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .ticket-type {
          font-size: 14px;
          color: #333;
        }

        .ticket-price {
          font-size: 16px;
          font-weight: 600;
          color: #9AFF02;
        }
      }
    }
  }
}

/* 底部安全区域 */
.safe-area {
  height: calc(20px + env(safe-area-inset-bottom));
  background: #f8f9fa;
}
</style>
