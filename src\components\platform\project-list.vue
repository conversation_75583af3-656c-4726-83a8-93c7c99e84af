<template>
  <view class="project-list-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <up-loading-icon mode="spinner" size="40" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 项目列表 -->
    <view v-else-if="projectList.length > 0" class="project-list">
      <view 
        v-for="(item, index) in projectList" 
        :key="item.ProjectId" 
        class="project-item"
        @click="handleItemClick(item)"
      >
        <view class="project-card">
          <!-- 左侧预览图 -->
          <view class="project-preview">
            <image 
              v-if="item.coverUrl" 
              :src="item.coverUrl" 
              class="project-thumbnail"
              mode="aspectFill"
            />
            <view v-else class="thumbnail-placeholder">
              <text class="iconfont icon-video"></text>
            </view>
          </view>

          <!-- 中间内容区域 -->
          <view class="project-content">
            <view class="project-title" :title="item.title">
              {{ formatTitle(item.title) }}
            </view>
            <view class="project-meta">
              <view class="project-time">
                <text class="time-icon">🕒</text>
                <text class="time-value">{{ formatTime(item.createTime) }}</text>
              </view>
              <view v-if="item.description" class="project-source">
                <text class="source-icon">📝</text>
                <text class="source-value">{{ item.description }}</text>
              </view>
            </view>
          </view>

          <!-- 右侧状态和操作 -->
          <view class="project-actions">
            <up-tag 
              :text="getStatusConfig(item.status).label"
              :type="getStatusConfig(item.status).type"
              size="mini"
              class="status-tag"
            />
            <view class="action-buttons">
              <!-- <up-button 
                type="primary" 
                size="mini" 
                @click.stop="handleEdit(item)"
                class="edit-btn"
              >
                编辑
              </up-button> -->
              <up-button 
                type="error" 
                size="mini" 
                @click.stop="handleDelete(item)"
                class="delete-btn"
              >
                删除
              </up-button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-container">
      <view class="empty-icon">
        <text class="iconfont icon-empty"></text>
      </view>
      <text class="empty-text">暂无项目数据</text>
      <up-button 
        type="primary" 
        @click="handleCreate"
        class="empty-create-btn"
      >
        创建新工程
      </up-button>
    </view>
  </view>
</template>

<script setup>
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  projectList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Events 定义
const emit = defineEmits(['itemClick', 'edit', 'delete', 'create'])

// 状态配置
const statusConfig = {
  Draft: { label: "草稿", type: "info" },
  Editing: { label: "编辑中", type: "primary" },
  Producing: { label: "制作中", type: "warning" },
  Produced: { label: "已制作完成", type: "success" },
  ProduceFailed: { label: "制作失败", type: "error" },
  Normal: { label: "正常", type: "success" },
  default: { label: "未知", type: "info" },
}

// 获取状态配置
function getStatusConfig(status) {
  console.log("getStatusConfig:", status)
  const statusStr = String(status)
  return statusConfig[statusStr] || statusConfig.default
}

// 格式化标题 - 智能截断长标题
function formatTitle(title) {
  if (!title) return ""

  // 如果标题长度超过20个字符，进行智能截断
  if (title.length > 20) {
    return title.substring(0, 18) + "..."
  }

  return title
}

// 格式化时间
function formatTime(timeStr) {
  if (!timeStr) return ""

  try {
    const date = new Date(timeStr)
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return timeStr
    }

    const now = new Date()
    const diff = now.getTime() - date.getTime()

    // 小于1分钟
    if (diff < 60 * 1000) {
      return "刚刚"
    }
    // 小于1小时
    if (diff < 60 * 60 * 1000) {
      return `${Math.floor(diff / (60 * 1000))}分钟前`
    }
    // 小于1天
    if (diff < 24 * 60 * 60 * 1000) {
      return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
    }
    // 大于1天，使用parseTime格式化
    return parseTime(timeStr, "{y}-{m}-{d} {h}:{i}")
  } catch (error) {
    console.error("时间格式化失败:", error)
    return timeStr || ""
  }
}

// 事件处理
function handleItemClick(item) {
  emit('itemClick', item)
}

function handleEdit(item) {
  emit('edit', item)
}

function handleDelete(item) {
  emit('delete', item)
}

function handleCreate() {
  emit('create')
}
</script>

<style lang="scss" scoped>
.project-list-container {
  // 加载状态
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;

    .loading-text {
      margin-top: 20rpx;
      color: #999;
      font-size: 28rpx;
    }
  }

  // 项目列表
  .project-list {
    .project-item {
      margin-bottom: 16rpx;
      .project-card {
        background-color: #fff;
        border-radius: 20rpx;
        padding: 20rpx;
        box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
        display: flex;
        align-items: center;
        transition: all 0.2s ease;
        border: 1rpx solid #f0f0f0;

        &:active {
          transform: translateY(-2rpx);
          box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
          border-color: #e0e0e0;
        }

        // 左侧预览图
        .project-preview {
          width: 100rpx;
          height: 100rpx;
          border-radius: 16rpx;
          overflow: hidden;
          margin-right: 20rpx;
          flex-shrink: 0;

          .project-thumbnail {
            width: 100%;
            height: 100%;
          }

          .thumbnail-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;

            .iconfont {
              font-size: 40rpx;
              color: #fff;

              &.icon-video:before {
                content: "\e68c";
              }
            }
          }
        }

        // 中间内容区域
        .project-content {
          flex: 1;
          margin-right: 16rpx;
          min-width: 0; // 确保flex子项能够收缩

          .project-title {
            font-size: 30rpx;
            font-weight: 600;
            color: #333;
            line-height: 1.3;
            margin-bottom: 12rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
          }

          .project-meta {
            display: flex;
            flex-direction: column;
            gap: 8rpx;

            .project-time,
            .project-source {
              font-size: 24rpx;
              color: #666;
              display: flex;
              align-items: center;
              line-height: 1.2;

              .time-icon,
              .source-icon {
                margin-right: 8rpx;
                font-size: 20rpx;
              }

              .time-value,
              .source-value {
                color: #888;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }

        // 右侧状态和操作
        .project-actions {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 16rpx;
          flex-shrink: 0;

          .status-tag {
            margin-bottom: 4rpx;
          }

          .action-buttons {
            display: flex;
            gap: 12rpx;

            .edit-btn,
            .delete-btn {
              padding: 12rpx 20rpx;
              font-size: 24rpx;
              border-radius: 12rpx;
              min-width: 80rpx;
              text-align: center;
            }
          }
        }
      }
    }
  }

  // 空状态
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;

    .empty-icon {
      width: 100rpx;
      height: 100rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 32rpx;

      .iconfont {
        font-size: 50rpx;
        color: #fff;

        &.icon-empty:before {
          content: "\e69a";
        }
      }
    }

    .empty-text {
      font-size: 30rpx;
      color: #666;
      margin-bottom: 48rpx;
      text-align: center;
    }

    .empty-create-btn {
      padding: 20rpx 40rpx;
      border-radius: 25rpx;
      font-size: 28rpx;
    }
  }
}
</style>
